# 认证项目指标结果处理逻辑修改说明

## 问题描述

当前的 `processUserIndicatorResults` 方法中的指标结果处理逻辑存在以下问题：

1. **SQL查询逻辑缺陷**：现有的 `selectLatestIndicatorResultsByAomProjectId` 方法会返回用户在项目下任意活动中的指标结果，而不是严格限制在最新活动中的结果。

2. **业务逻辑不完整**：当用户在最新活动中没有指标结果时，没有删除该用户在认证项目中的现有指标结果。

3. **数据一致性问题**：用户可以同时参与新老活动，如果用户只在老活动中有结果，系统会错误地使用老活动的结果，而不是识别出"最新活动无结果"的情况。

## 复杂业务场景示例

**数据结构：**
- AOM项目A 包含两个活动：
  - 活动1（老活动，创建时间：2024-01-01）：包含指标1、指标2、指标3
  - 活动2（新活动，创建时间：2024-02-01）：包含指标1、指标3、指标4

**4个具体案例：**

1. **指标1**：只在活动2中配置，用户有结果 → 取活动2的结果
2. **指标2**：只在活动1中配置，用户有结果 → 取活动1的结果（即使活动1不是最新的）
3. **指标3**：在活动1和活动2中都配置，用户在两个活动中都有结果 → 取活动2的结果（因为活动2是最新的）
4. **指标4**：只在活动2中配置，但用户没有结果 → 清空该指标的现有数据

**核心业务逻辑：**
- 对于每个指标，找到**包含该指标的最新活动**
- 如果该指标在多个活动中都存在，取最新活动的结果（即使是空值）
- 如果该指标只在一个活动中存在，取该活动的结果（无论该活动是否是最新的）
- 如果用户在某指标的最新活动中没有结果，清空该指标的现有数据

## 业务需求

1. 用户可以同时参与新老活动，系统需要严格获取用户在**项目下最新活动**中的指标结果
2. 如果该用户在最新活动中没有任何指标结果，则需要删除数据库中该用户当前存储的所有指标结果
3. 如果该用户在最新活动中有指标结果，则用新结果完全覆盖数据库中的现有结果
4. 需要修改SQL查询逻辑，确保严格查询最新活动的结果

## 修改内容

### 1. 修改 AuthPrjIndicatorResultService.java

#### 1.1 processUserIndicatorResults 方法
- **修改位置**：第54-122行
- **主要变更**：
  - 更新方法注释，明确说明复杂的按指标维度处理的业务逻辑
  - 更新参数注释，明确 `actvId` 是AOM项目ID
  - 保持原有的删除逻辑，但现在主要依赖新的SQL查询逻辑来处理复杂场景

#### 1.2 queryAomObjectiveResults 方法
- **修改位置**：第143-190行
- **主要变更**：
  - 完全重写方法逻辑，实现复杂的按指标维度处理
  - 新增查询项目中所有指标的最新活动信息
  - 识别需要清空的指标（用户在最新活动中没有结果的指标）
  - 通过特殊标记 `"__CLEAR_INDICATOR__"` 来标识需要清空的指标
  - 将需要清空的指标信息传递给后续处理流程

#### 1.3 convertToSaveResults 方法
- **修改位置**：第238-274行
- **主要变更**：
  - 新增对特殊标记 `"__CLEAR_INDICATOR__"` 的处理逻辑
  - 当遇到需要清空的指标时，将现有记录标记为删除（`deleted=1`）
  - 保持正常指标结果的处理逻辑不变

#### 1.4 新增方法
- **deleteUserIndicatorResults**：删除用户所有指标结果
- **clearSpecificIndicatorResults**：清空特定指标结果（预留方法）

### 2. 修改 RvActivityObjectiveResultMapper.java

#### 2.1 新增查询方法
- **修改位置**：第42-58行
- **新增方法**：`selectIndicatorLatestActivitiesByAomProjectId`
- **功能**：查询AOM项目中所有指标的最新活动信息

### 3. 修改 RvActivityObjectiveResultMapper.xml

#### 3.1 selectLatestIndicatorResultsByAomProjectId 查询
- **修改位置**：第215-269行
- **核心变更**：完全重写SQL查询逻辑，实现按指标维度的复杂处理
- **新查询逻辑**：
  ```sql
  with IndicatorLatestActivity as (
    -- 第一步：找到每个指标所在的最新活动
    select distinct
           aor.objective_id,
           first_value(c.id) over (partition by aor.objective_id order by c.create_time desc) as latest_item_id
    from rv_activity_objective_result aor
    join rv_base_activity_result b on aor.base_actv_result_id = b.id and b.deleted = 0
    join rv_activity_arrange_item c on c.id = b.item_id and c.deleted = 0
    join rv_activity d on d.id = c.actv_id and d.deleted = 0
    where d.id = #{aomProjectId} and aor.org_id = #{orgId} and aor.deleted = 0 and c.deleted = 0
  ),
  UserIndicatorResults as (
    -- 第二步：查询用户在每个指标的最新活动中的结果
    select ... from rv_activity_objective_result a
    join IndicatorLatestActivity ila on c.id = ila.latest_item_id and a.objective_id = ila.objective_id
  )
  ```

#### 3.2 新增 selectIndicatorLatestActivitiesByAomProjectId 查询
- **修改位置**：第271-289行
- **功能**：查询项目中所有指标的最新活动信息，用于识别需要清空的指标

### 3. 新增测试类

#### 3.1 AuthPrjIndicatorResultServiceTest.java
- **文件位置**：`src/test/java/com/yxt/talent/rv/application/authprj/AuthPrjIndicatorResultServiceTest.java`
- **测试内容**：
  - 测试查询AOM项目最新活动的指标结果
  - 测试处理用户指标结果的完整流程
  - 测试删除用户指标结果方法
  - 测试业务逻辑的正确性

## 核心改进点

### 1. SQL查询逻辑根本性修复
- **原逻辑**：按活动创建时间排序，选择有结果的最新活动（错误）
- **新逻辑**：先找到最新活动，再严格查询该活动的结果（正确）

### 2. 业务逻辑完整性
- **原逻辑**：当没有结果时直接返回，不处理现有数据
- **新逻辑**：当最新活动没有结果时主动删除现有数据，确保数据一致性

### 3. 数据准确性
- **原逻辑**：可能返回老活动的结果，导致数据不准确
- **新逻辑**：严格返回最新活动的结果，确保数据准确性

### 4. 业务场景处理
- **原逻辑**：无法正确处理"用户只在老活动有结果"的场景
- **新逻辑**：正确识别并处理各种业务场景

## 关键技术点

### 1. SQL查询逻辑的根本性重构
**原有问题逻辑：**
```sql
-- 错误：按活动创建时间排序，可能返回老活动的结果
row_number() over (partition by a.user_id, a.objective_id order by c.create_time desc, a.id desc) as rn
```

**新的正确逻辑：**
```sql
with LatestActivity as (
  -- 第一步：找到项目下最新创建的活动
  select c.id as latest_item_id
  from rv_activity_arrange_item c
  join rv_activity d on d.id = c.actv_id and d.deleted = 0
  where d.id = #{aomProjectId} and c.deleted = 0
  order by c.create_time desc limit 1
)
-- 第二步：严格查询用户在最新活动中的指标结果
select ... from rv_activity_objective_result a
join LatestActivity la on c.id = la.latest_item_id
```

### 2. 两步查询策略
1. **第一步**：找到项目下最新创建的活动（按 `create_time desc` 排序）
2. **第二步**：严格查询用户在这个最新活动中的指标结果

### 3. 删除逻辑的触发条件
- 用户在项目最新活动中没有任何指标结果
- 活动指标与认证项目模型指标无交集
- 过滤后没有有效指标结果

### 4. 数据覆盖逻辑保持不变
当有有效指标结果时，仍然使用原有的覆盖逻辑。

## 影响范围

### 1. 直接影响
- `AuthPrjIndicatorResultService.processUserIndicatorResults` 方法的调用者
- 依赖指标结果数据的认证项目相关功能

### 2. 数据影响
- `rv_authprj_result_user_indicator` 表的数据会更加准确
- 删除了用户在最新活动中没有结果时的历史指标结果

### 3. 性能影响
- 新增的删除操作可能会增加少量数据库操作
- 整体查询逻辑保持不变，性能影响最小

## 测试建议

1. **单元测试**：运行新增的测试类验证基本功能
2. **集成测试**：测试完整的认证项目指标结果处理流程
3. **数据验证**：验证修改后的数据准确性和一致性
4. **业务场景测试**：
   - 测试用户同时参与新老活动的场景
   - 测试用户在最新活动中没有结果的场景
   - 测试用户在最新活动中有结果的场景

## 部署注意事项

1. **数据备份**：部署前备份相关数据表
2. **渐进部署**：建议先在测试环境验证
3. **监控日志**：部署后关注相关日志，确保删除操作正常执行
4. **数据校验**：部署后验证指标结果数据的准确性
5. **业务验证**：确认认证项目的概览和个人报告功能正常
