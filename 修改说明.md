# 认证项目指标结果处理逻辑修改说明

## 问题描述

当前的 `processUserIndicatorResults` 方法中的指标结果处理逻辑存在以下问题：

1. **SQL查询逻辑缺陷**：现有的 `selectLatestIndicatorResultsByAomProjectId` 方法会返回用户在项目下任意活动中的指标结果，而不是严格限制在最新活动中的结果。

2. **业务逻辑不完整**：当用户在最新活动中没有指标结果时，没有删除该用户在认证项目中的现有指标结果。

3. **数据一致性问题**：用户可以同时参与新老活动，如果用户只在老活动中有结果，系统会错误地使用老活动的结果，而不是识别出"最新活动无结果"的情况。

## 业务场景示例

**数据结构：**
- AOM项目A 包含两个活动：
  - 活动1（老活动，创建时间：2024-01-01）
  - 活动2（新活动，创建时间：2024-02-01）

**问题场景：**
- 用户在活动1中有指标结果
- 用户在活动2中没有指标结果
- 传入的 `actvId` = 项目A的ID

**当前错误行为：**
- SQL查询会返回活动1的指标结果（因为按创建时间排序，活动1有结果）
- 系统认为用户有指标结果，保存到认证项目中

**期望正确行为：**
- SQL查询应该严格查询活动2（最新活动）的指标结果
- 发现活动2没有结果，返回空列表
- 系统删除用户在认证项目中的现有指标结果

## 业务需求

1. 用户可以同时参与新老活动，系统需要严格获取用户在**项目下最新活动**中的指标结果
2. 如果该用户在最新活动中没有任何指标结果，则需要删除数据库中该用户当前存储的所有指标结果
3. 如果该用户在最新活动中有指标结果，则用新结果完全覆盖数据库中的现有结果
4. 需要修改SQL查询逻辑，确保严格查询最新活动的结果

## 修改内容

### 1. 修改 AuthPrjIndicatorResultService.java

#### 1.1 processUserIndicatorResults 方法
- **修改位置**：第43-122行
- **主要变更**：
  - 更新方法注释，明确说明用户可以同时参与新老活动的业务场景
  - 更新参数注释，明确 `actvId` 实际上是AOM项目ID
  - 增加了当查询不到最新活动的指标结果时的删除逻辑
  - 在三个关键点调用 `deleteUserIndicatorResults` 方法：
    - 未找到用户在最新活动中的AOM指标结果数据时
    - 活动指标与模型指标无交集时
    - 过滤后无有效指标结果时
  - 更新了日志信息，明确标识删除操作和AOM项目ID

#### 1.2 queryAomObjectiveResults 方法
- **修改位置**：第136-159行
- **主要变更**：
  - 更新方法注释，明确说明获取最新活动结果的逻辑
  - 更新参数名称从 `actvId` 到 `aomProjectId`，更准确地反映参数含义
  - 保持使用原有的 `selectLatestIndicatorResultsByAomProjectId` 方法
  - 更新日志信息，明确标识AOM项目ID

### 2. 修改 RvActivityObjectiveResultMapper.xml

#### 2.1 selectLatestIndicatorResultsByAomProjectId 查询
- **修改位置**：第215-261行
- **核心变更**：完全重写SQL查询逻辑
- **原查询逻辑**：
  ```sql
  -- 按用户和指标分区，按活动创建时间降序排名（有问题的逻辑）
  row_number() over (partition by a.user_id, a.objective_id order by c.create_time desc, a.id desc) as rn
  ```
- **新查询逻辑**：
  ```sql
  with LatestActivity as (
    -- 第一步：找到项目下最新创建的活动
    select c.id as latest_item_id, c.create_time as latest_create_time
    from rv_activity_arrange_item c
    join rv_activity d on d.id = c.actv_id and d.deleted = 0
    where d.id = #{aomProjectId} and c.deleted = 0
    order by c.create_time desc limit 1
  )
  -- 第二步：严格查询用户在最新活动中的指标结果
  select ... from rv_activity_objective_result a
  join LatestActivity la on c.id = la.latest_item_id
  ```

### 3. 新增测试类

#### 3.1 AuthPrjIndicatorResultServiceTest.java
- **文件位置**：`src/test/java/com/yxt/talent/rv/application/authprj/AuthPrjIndicatorResultServiceTest.java`
- **测试内容**：
  - 测试查询AOM项目最新活动的指标结果
  - 测试处理用户指标结果的完整流程
  - 测试删除用户指标结果方法
  - 测试业务逻辑的正确性

## 核心改进点

### 1. SQL查询逻辑根本性修复
- **原逻辑**：按活动创建时间排序，选择有结果的最新活动（错误）
- **新逻辑**：先找到最新活动，再严格查询该活动的结果（正确）

### 2. 业务逻辑完整性
- **原逻辑**：当没有结果时直接返回，不处理现有数据
- **新逻辑**：当最新活动没有结果时主动删除现有数据，确保数据一致性

### 3. 数据准确性
- **原逻辑**：可能返回老活动的结果，导致数据不准确
- **新逻辑**：严格返回最新活动的结果，确保数据准确性

### 4. 业务场景处理
- **原逻辑**：无法正确处理"用户只在老活动有结果"的场景
- **新逻辑**：正确识别并处理各种业务场景

## 关键技术点

### 1. SQL查询逻辑的根本性重构
**原有问题逻辑：**
```sql
-- 错误：按活动创建时间排序，可能返回老活动的结果
row_number() over (partition by a.user_id, a.objective_id order by c.create_time desc, a.id desc) as rn
```

**新的正确逻辑：**
```sql
with LatestActivity as (
  -- 第一步：找到项目下最新创建的活动
  select c.id as latest_item_id
  from rv_activity_arrange_item c
  join rv_activity d on d.id = c.actv_id and d.deleted = 0
  where d.id = #{aomProjectId} and c.deleted = 0
  order by c.create_time desc limit 1
)
-- 第二步：严格查询用户在最新活动中的指标结果
select ... from rv_activity_objective_result a
join LatestActivity la on c.id = la.latest_item_id
```

### 2. 两步查询策略
1. **第一步**：找到项目下最新创建的活动（按 `create_time desc` 排序）
2. **第二步**：严格查询用户在这个最新活动中的指标结果

### 3. 删除逻辑的触发条件
- 用户在项目最新活动中没有任何指标结果
- 活动指标与认证项目模型指标无交集
- 过滤后没有有效指标结果

### 4. 数据覆盖逻辑保持不变
当有有效指标结果时，仍然使用原有的覆盖逻辑。

## 影响范围

### 1. 直接影响
- `AuthPrjIndicatorResultService.processUserIndicatorResults` 方法的调用者
- 依赖指标结果数据的认证项目相关功能

### 2. 数据影响
- `rv_authprj_result_user_indicator` 表的数据会更加准确
- 删除了用户在最新活动中没有结果时的历史指标结果

### 3. 性能影响
- 新增的删除操作可能会增加少量数据库操作
- 整体查询逻辑保持不变，性能影响最小

## 测试建议

1. **单元测试**：运行新增的测试类验证基本功能
2. **集成测试**：测试完整的认证项目指标结果处理流程
3. **数据验证**：验证修改后的数据准确性和一致性
4. **业务场景测试**：
   - 测试用户同时参与新老活动的场景
   - 测试用户在最新活动中没有结果的场景
   - 测试用户在最新活动中有结果的场景

## 部署注意事项

1. **数据备份**：部署前备份相关数据表
2. **渐进部署**：建议先在测试环境验证
3. **监控日志**：部署后关注相关日志，确保删除操作正常执行
4. **数据校验**：部署后验证指标结果数据的准确性
5. **业务验证**：确认认证项目的概览和个人报告功能正常
