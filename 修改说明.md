# 认证项目指标结果处理逻辑修改说明

## 问题描述

当前的 `processUserIndicatorResults` 方法中的指标结果处理逻辑存在以下问题：

1. **业务逻辑不完整**：当用户在最新活动中没有指标结果时，没有删除该用户在认证项目中的现有指标结果。

2. **数据一致性问题**：用户可以同时参与新老活动，但系统没有正确处理"最新活动无结果"的情况，导致旧数据残留。

3. **参数理解偏差**：传入的 `actvId` 实际上是AOM项目ID，而不是具体的活动ID。

## 业务需求

1. 用户可以同时参与新老活动，系统需要获取用户在**最新活动**中的指标结果
2. 如果该用户在最新活动中没有任何指标结果，则需要删除数据库中该用户当前存储的所有指标结果
3. 如果该用户在最新活动中有指标结果，则用新结果完全覆盖数据库中的现有结果
4. 现有的SQL查询 `selectLatestIndicatorResultsByAomProjectId` 已经能够正确获取最新活动结果，无需修改

## 修改内容

### 1. 修改 AuthPrjIndicatorResultService.java

#### 1.1 processUserIndicatorResults 方法
- **修改位置**：第43-122行
- **主要变更**：
  - 更新方法注释，明确说明用户可以同时参与新老活动的业务场景
  - 更新参数注释，明确 `actvId` 实际上是AOM项目ID
  - 增加了当查询不到最新活动的指标结果时的删除逻辑
  - 在三个关键点调用 `deleteUserIndicatorResults` 方法：
    - 未找到用户在最新活动中的AOM指标结果数据时
    - 活动指标与模型指标无交集时
    - 过滤后无有效指标结果时
  - 更新了日志信息，明确标识删除操作和AOM项目ID

#### 1.2 queryAomObjectiveResults 方法
- **修改位置**：第136-159行
- **主要变更**：
  - 更新方法注释，明确说明获取最新活动结果的逻辑
  - 更新参数名称从 `actvId` 到 `aomProjectId`，更准确地反映参数含义
  - 保持使用原有的 `selectLatestIndicatorResultsByAomProjectId` 方法
  - 更新日志信息，明确标识AOM项目ID

### 2. 新增测试类

#### 2.1 AuthPrjIndicatorResultServiceTest.java
- **文件位置**：`src/test/java/com/yxt/talent/rv/application/authprj/AuthPrjIndicatorResultServiceTest.java`
- **测试内容**：
  - 测试查询AOM项目最新活动的指标结果
  - 测试处理用户指标结果的完整流程
  - 测试删除用户指标结果方法
  - 测试业务逻辑的正确性

## 核心改进点

### 1. 业务逻辑完整性
- **原逻辑**：当没有结果时直接返回，不处理现有数据
- **新逻辑**：当没有结果时主动删除现有数据，确保数据一致性

### 2. 参数理解准确性
- **原理解**：`actvId` 是具体的活动ID
- **新理解**：`actvId` 是AOM项目ID，SQL会自动获取该项目下用户最新活动的结果

### 3. 数据一致性
- **原逻辑**：可能存在旧数据残留
- **新逻辑**：确保数据与用户最新活动状态保持一致

### 4. 日志清晰性
- **原日志**：使用 `actvId` 标识，可能造成混淆
- **新日志**：使用 `aomProjectId` 标识，更加准确

## 关键技术点

### 1. SQL查询逻辑保持不变
现有的 `selectLatestIndicatorResultsByAomProjectId` 方法已经能够正确处理业务需求：
```sql
-- 核心去重逻辑: 按用户和指标分区，按活动创建时间降序排名
row_number() over (partition by a.user_id, a.objective_id order by c.create_time desc, a.id desc) as rn
```

### 2. 删除逻辑的触发条件
- 用户在最新活动中没有任何指标结果
- 活动指标与认证项目模型指标无交集
- 过滤后没有有效指标结果

### 3. 数据覆盖逻辑保持不变
当有有效指标结果时，仍然使用原有的覆盖逻辑。

## 影响范围

### 1. 直接影响
- `AuthPrjIndicatorResultService.processUserIndicatorResults` 方法的调用者
- 依赖指标结果数据的认证项目相关功能

### 2. 数据影响
- `rv_authprj_result_user_indicator` 表的数据会更加准确
- 删除了用户在最新活动中没有结果时的历史指标结果

### 3. 性能影响
- 新增的删除操作可能会增加少量数据库操作
- 整体查询逻辑保持不变，性能影响最小

## 测试建议

1. **单元测试**：运行新增的测试类验证基本功能
2. **集成测试**：测试完整的认证项目指标结果处理流程
3. **数据验证**：验证修改后的数据准确性和一致性
4. **业务场景测试**：
   - 测试用户同时参与新老活动的场景
   - 测试用户在最新活动中没有结果的场景
   - 测试用户在最新活动中有结果的场景

## 部署注意事项

1. **数据备份**：部署前备份相关数据表
2. **渐进部署**：建议先在测试环境验证
3. **监控日志**：部署后关注相关日志，确保删除操作正常执行
4. **数据校验**：部署后验证指标结果数据的准确性
5. **业务验证**：确认认证项目的概览和个人报告功能正常
