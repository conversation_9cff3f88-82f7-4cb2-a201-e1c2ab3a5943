package com.yxt.talent.rv.application.authprj;

import com.yxt.talent.rv.BaseSpringBootTest;
import com.yxt.talent.rv.application.aom.dto.ActivityObjectiveResultWithActivityInfoDTO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityObjectiveResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjResultUserIndicatorMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserIndicatorPO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 认证项目指标结果处理服务测试
 * 测试修改后的业务逻辑：当用户在最新活动中没有指标结果时，删除现有数据
 */
@Transactional
@Rollback
public class AuthPrjIndicatorResultServiceTest extends BaseSpringBootTest {

    @Autowired
    private AuthPrjIndicatorResultService authPrjIndicatorResultService;

    @Autowired
    private RvActivityObjectiveResultMapper rvActivityObjectiveResultMapper;

    @Autowired
    private AuthprjResultUserIndicatorMapper authprjResultUserIndicatorMapper;

    /**
     * 测试查询AOM项目最新活动的指标结果
     * 验证SQL查询方法能够正确获取用户在最新活动中的结果
     */
    @Test
    public void testQueryLatestActivityResults() {
        // 测试参数
        String orgId = "test-org-id";
        String aomProjectId = "test-aom-project-id";
        String userId = "test-user-id";

        // 调用查询方法
        List<ActivityObjectiveResultWithActivityInfoDTO> results = 
            rvActivityObjectiveResultMapper.selectLatestIndicatorResultsByAomProjectId(orgId, aomProjectId, userId);

        // 验证结果不为null
        assertNotNull(results);
        
        // 如果有结果，验证所有结果都属于指定的用户和机构
        for (ActivityObjectiveResultWithActivityInfoDTO result : results) {
            assertEquals(orgId, result.getOrgId());
            assertEquals(userId, result.getUserId());
            // 验证结果包含活动信息
            assertNotNull(result.getActvId());
        }
    }

    /**
     * 测试处理用户指标结果的完整流程
     * 验证当没有指标结果时是否正确删除现有数据
     */
    @Test
    public void testProcessUserIndicatorResultsWithNoResults() {
        // 测试参数
        String orgId = "test-org-id";
        String authPrjId = "test-auth-prj-id";
        String userId = "test-user-id";
        String aomProjectId = "non-existent-aom-project-id";

        // 先查询现有的指标结果数量
        List<AuthprjResultUserIndicatorPO> existingResultsBefore = 
            authprjResultUserIndicatorMapper.selectByAuthprjIdAndUserId(orgId, authPrjId, userId);
        int countBefore = existingResultsBefore.size();

        // 调用处理方法（预期不会找到最新活动的结果）
        authPrjIndicatorResultService.processUserIndicatorResults(orgId, authPrjId, userId, aomProjectId);

        // 验证处理后的结果
        List<AuthprjResultUserIndicatorPO> existingResultsAfter = 
            authprjResultUserIndicatorMapper.selectByAuthprjIdAndUserId(orgId, authPrjId, userId);
        int countAfter = existingResultsAfter.size();

        // 如果之前有数据，处理后应该被删除
        if (countBefore > 0) {
            assertTrue(countAfter <= countBefore, "处理后的指标结果数量应该不大于处理前");
        }
    }

    /**
     * 测试删除用户指标结果方法
     */
    @Test
    public void testDeleteUserIndicatorResults() {
        // 测试参数
        String orgId = "test-org-id";
        String authPrjId = "test-auth-prj-id";
        String userId = "test-user-id";

        // 先查询现有的指标结果数量
        List<AuthprjResultUserIndicatorPO> existingResultsBefore = 
            authprjResultUserIndicatorMapper.selectByAuthprjIdAndUserId(orgId, authPrjId, userId);
        int countBefore = existingResultsBefore.size();

        // 调用删除方法
        authPrjIndicatorResultService.deleteUserIndicatorResults(orgId, authPrjId, userId);

        // 验证删除后的结果
        List<AuthprjResultUserIndicatorPO> existingResultsAfter = 
            authprjResultUserIndicatorMapper.selectByAuthprjIdAndUserId(orgId, authPrjId, userId);
        int countAfter = existingResultsAfter.size();

        // 删除后数量应该为0或者小于删除前
        assertTrue(countAfter <= countBefore, "删除后的指标结果数量应该不大于删除前");
    }

    /**
     * 测试业务逻辑的正确性
     * 验证修改后的逻辑能够正确处理"用户可以同时参与新老活动"的场景
     */
    @Test
    public void testBusinessLogicCorrectness() {
        // 测试参数
        String orgId = "test-org-id";
        String userId = "test-user-id";
        String aomProjectId = "test-aom-project-id";

        // 调用查询方法，验证能够获取到最新活动的结果
        List<ActivityObjectiveResultWithActivityInfoDTO> results = 
            rvActivityObjectiveResultMapper.selectLatestIndicatorResultsByAomProjectId(orgId, aomProjectId, userId);

        // 验证结果的特点
        assertNotNull(results);

        // 如果有多个结果，验证它们都是最新的（通过SQL的去重逻辑保证）
        System.out.println("查询到的最新活动指标结果数量: " + results.size());
        
        // 验证每个结果都包含必要的信息
        for (ActivityObjectiveResultWithActivityInfoDTO result : results) {
            assertNotNull(result.getObjectiveId(), "指标ID不能为空");
            assertNotNull(result.getActvId(), "活动ID不能为空");
            assertNotNull(result.getActivityName(), "活动名称不能为空");
            assertNotNull(result.getProjectName(), "项目名称不能为空");
        }
    }
}
