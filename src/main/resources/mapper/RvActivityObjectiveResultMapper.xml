<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityObjectiveResultMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityObjectiveResultPO">
    <!--@mbg.generated-->
    <!--@Table rv_activity_objective_result-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="actv_id" property="actvId" />
    <result column="user_id" property="userId" />
    <result column="base_actv_result_id" property="baseActvResultId" />
    <result column="objective_id" property="objectiveId" />
    <result column="objective_mode_id" property="objectiveModeId" />
    <result column="objective_type" property="objectiveType" />
    <result column="objective_score" property="objectiveScore" />
    <result column="objective_total_score" property="objectiveTotalScore" />
    <result column="objective_level" property="objectiveLevel" />
    <result column="objective_result" property="objectiveResult" />
    <result column="deleted" property="deleted" />
    <result column="create_time" property="createTime" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_time" property="updateTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="db_archived" property="dbArchived" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, actv_id, user_id, base_actv_result_id, objective_id, objective_mode_id, 
    objective_type, objective_score, objective_total_score, objective_level, objective_result, 
    deleted, create_time, create_user_id, update_time, update_user_id, db_archived
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_activity_objective_result
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityObjectiveResultPO">
    <!--@mbg.generated-->
    insert into rv_activity_objective_result (id, org_id, actv_id, user_id, base_actv_result_id, objective_id, 
      objective_mode_id, objective_type, objective_score, objective_total_score, 
      objective_level, objective_result, deleted, create_time, create_user_id, 
      update_time, update_user_id, db_archived)
    values (#{id}, #{orgId}, #{actvId}, #{userId}, #{baseActvResultId}, #{objectiveId}, 
      #{objectiveModeId}, #{objectiveType}, #{objectiveScore}, #{objectiveTotalScore}, 
      #{objectiveLevel}, #{objectiveResult}, #{deleted}, #{createTime}, #{createUserId}, 
      #{updateTime}, #{updateUserId}, #{dbArchived})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityObjectiveResultPO">
    <!--@mbg.generated-->
    update rv_activity_objective_result
    set org_id = #{orgId},
      actv_id = #{actvId},
      user_id = #{userId},
      base_actv_result_id = #{baseActvResultId},
      objective_id = #{objectiveId},
      objective_mode_id = #{objectiveModeId},
      objective_type = #{objectiveType},
      objective_score = #{objectiveScore},
      objective_total_score = #{objectiveTotalScore},
      objective_level = #{objectiveLevel},
      objective_result = #{objectiveResult},
      deleted = #{deleted},
      create_time = #{createTime},
      create_user_id = #{createUserId},
      update_time = #{updateTime},
      update_user_id = #{updateUserId},
      db_archived = #{dbArchived}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_activity_objective_result
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="actv_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.actvId}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userId}
        </foreach>
      </trim>
      <trim prefix="base_actv_result_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.baseActvResultId}
        </foreach>
      </trim>
      <trim prefix="objective_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveId}
        </foreach>
      </trim>
      <trim prefix="objective_mode_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveModeId}
        </foreach>
      </trim>
      <trim prefix="objective_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveType}
        </foreach>
      </trim>
      <trim prefix="objective_score = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveScore}
        </foreach>
      </trim>
      <trim prefix="objective_total_score = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveTotalScore}
        </foreach>
      </trim>
      <trim prefix="objective_level = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveLevel}
        </foreach>
      </trim>
      <trim prefix="objective_result = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveResult}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="db_archived = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.dbArchived}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_activity_objective_result
    (id, org_id, actv_id, user_id, base_actv_result_id, objective_id, objective_mode_id, 
      objective_type, objective_score, objective_total_score, objective_level, objective_result, 
      deleted, create_time, create_user_id, update_time, update_user_id, db_archived)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.actvId}, #{item.userId}, #{item.baseActvResultId}, 
        #{item.objectiveId}, #{item.objectiveModeId}, #{item.objectiveType}, #{item.objectiveScore}, 
        #{item.objectiveTotalScore}, #{item.objectiveLevel}, #{item.objectiveResult}, #{item.deleted}, 
        #{item.createTime}, #{item.createUserId}, #{item.updateTime}, #{item.updateUserId}, 
        #{item.dbArchived})
    </foreach>
  </insert>

  <select id="queryByUserIds" resultType="com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto">
    select aor.user_id,
    aor.actv_id as ref_id,
    aor.objective_id as sd_indicator_id,
    aor.objective_score as score,
    if(aor.objective_result in (1,3),1,0) as qualified,
    aor.ext as ext_json from rv_activity_objective_result aor
    where aor.org_id = #{orgId} and aor.deleted = 0
    and aor.actv_id in
    <foreach collection="actvIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and aor.user_id in
    <foreach collection="userIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and aor.objective_id in
    <foreach collection="objectiveIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectByActvIdAndUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_activity_objective_result
    where org_id = #{orgId}
      and actv_id = #{actvId}
      and user_id = #{userId}
      and deleted = 0
  </select>

  <select id="selectLatestIndicatorResultsByAomProjectId"
          resultType="com.yxt.talent.rv.application.aom.dto.ActivityObjectiveResultWithActivityInfoDTO">
    with IndicatorLatestActivity as (
      -- 第一步：找到每个指标所在的最新活动
      -- 对于每个指标，选择包含该指标的创建时间最新的活动
      select distinct
             aor.objective_id,
             first_value(c.id) over (partition by aor.objective_id order by c.create_time desc) as latest_item_id
      from rv_activity_objective_result aor
      join rv_base_activity_result b on aor.base_actv_result_id = b.id and b.deleted = 0
      join rv_activity_arrange_item c on c.id = b.item_id and c.deleted = 0
      join rv_activity d on d.id = c.actv_id and d.deleted = 0
      where d.id = #{aomProjectId}
        and aor.org_id = #{orgId}
        and aor.deleted = 0
        and c.deleted = 0
    ),
    UserIndicatorResults as (
      -- 第二步：查询用户在每个指标的最新活动中的结果
      -- 注意：如果用户在最新活动中没有该指标的结果，这里会查询不到数据
      select a.id
           , a.org_id
           , a.actv_id
           , a.user_id
           , a.base_actv_result_id
           , a.objective_id
           , a.objective_mode_id
           , a.objective_type
           , a.objective_score
           , a.objective_total_score
           , a.objective_level
           , a.objective_result
           , a.deleted
           , a.create_time
           , a.create_user_id
           , a.update_time
           , a.update_user_id
           , a.db_archived
           , c.ref_name                                                                                   as activity_name
           , c.ref_reg_id                                                                                 as activity_reg_id
           , c.create_time                                                                                as activity_create_time
           , d.actv_name                                                                                  as project_name
           , d.actv_reg_id                                                                                as project_reg_id
      from rv_activity_objective_result a
      join rv_base_activity_result      b on a.base_actv_result_id = b.id and b.deleted = 0
      join rv_activity_arrange_item     c on c.id = b.item_id and c.deleted = 0
      join rv_activity                  d on d.id = c.actv_id and d.deleted = 0
      join IndicatorLatestActivity      ila on c.id = ila.latest_item_id and a.objective_id = ila.objective_id
      where d.id = #{aomProjectId}
        and a.user_id = #{userId}
        and a.org_id = #{orgId}
        and a.deleted = 0
    )
    select * from UserIndicatorResults
  </select>

  <select id="selectIndicatorLatestActivitiesByAomProjectId"
          resultType="com.yxt.talent.rv.application.aom.dto.IndicatorLatestActivityDTO">
    -- 查询AOM项目中所有指标的最新活动信息
    select distinct
           aor.objective_id,
           first_value(c.id) over (partition by aor.objective_id order by c.create_time desc) as latest_item_id,
           first_value(c.ref_name) over (partition by aor.objective_id order by c.create_time desc) as latest_item_name,
           first_value(c.create_time) over (partition by aor.objective_id order by c.create_time desc) as latest_create_time,
           d.id as project_id,
           d.actv_name as project_name
    from rv_activity_objective_result aor
    join rv_base_activity_result b on aor.base_actv_result_id = b.id and b.deleted = 0
    join rv_activity_arrange_item c on c.id = b.item_id and c.deleted = 0
    join rv_activity d on d.id = c.actv_id and d.deleted = 0
    where d.id = #{aomProjectId}
      and aor.org_id = #{orgId}
      and aor.deleted = 0
      and c.deleted = 0
  </select>

  <select id="selectByActvIdAndRefIdAndUserId"
          resultType="com.yxt.talent.rv.application.authprj.dto.UserRefActiveResultDTO">
    select a.user_id
         , c.ref_name
         , c.ref_id
         , c.ref_reg_id
         , b.objective_mode_id
         , b.objective_total_score
         , b.objective_level
         , b.objective_result
         , b.objective_id
         , b.objective_score
    from rv_base_activity_result      a
    join rv_activity_arrange_item     c on c.id = a.item_id and c.org_id = a.org_id and c.deleted = 0
    join rv_activity_objective_result b on a.id = b.base_actv_result_id and a.org_id = b.org_id and b.deleted = 0
    where a.actv_id = #{actvId}
      and a.org_id = #{orgId}
      and a.user_id = #{userId}
      and c.ref_id =  #{refId}
      and a.deleted = 0
  </select>

</mapper>