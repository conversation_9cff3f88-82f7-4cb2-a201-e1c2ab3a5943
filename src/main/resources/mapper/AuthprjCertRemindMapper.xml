<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjCertRemindMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjCertRemindPO">
    <!--@mbg.generated-->
    <!--@Table rv_authprj_cert_remind-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="authprj_cert_id" property="authprjCertId" />
    <result column="remind_time" property="remindTime" />
    <result column="remind_type" property="remindType" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, authprj_cert_id, remind_time, remind_type, deleted, create_user_id, create_time, 
    update_user_id, update_time
  </sql>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_authprj_cert_remind
    (id, org_id, authprj_cert_id, remind_time, remind_type, deleted, create_user_id, 
      create_time, update_user_id, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.authprjCertId}, #{item.remindTime}, #{item.remindType}, 
        #{item.deleted}, #{item.createUserId}, #{item.createTime}, #{item.updateUserId}, 
        #{item.updateTime})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    authprj_cert_id=values(authprj_cert_id),
    remind_time=values(remind_time),
    remind_type=values(remind_type),
    deleted=values(deleted),
    create_user_id=values(create_user_id),
    create_time=values(create_time),
    update_user_id=values(update_user_id),
    update_time=values(update_time)
  </insert>

  <!-- 根据证书ID删除提醒配置 -->
  <update id="deleteByAuthprjCertId">
    UPDATE rv_authprj_cert_remind
    SET deleted = 1, update_time = NOW(), update_user_id = #{operatorId}
    WHERE org_id = #{orgId}
      AND authprj_cert_id = #{authprjCertId}
      AND deleted = 0
  </update>

  <select id="selectRemindListByAuthprjId"
          resultType="com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjCertRemindVO">
    select <include refid="Base_Column_List" />
    from rv_authprj_cert_remind a
    where a.org_id = #{orgId}
      and a.deleted = 0
      and exists (
        select 1
        from rv_authprj_cert b
        where b.id = a.authprj_cert_id
          and b.org_id = #{orgId}
          and b.deleted = 0
          and b.authprj_id = #{authprjId}
      )
    order by a.create_time
  </select>

  <!-- 查询所有活跃的证书提醒配置 -->
  <select id="selectAllActiveRemindConfigs"
          resultType="com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjCertRemindVO">
    select a.org_id, c.id as authprjId, a.id, a.authprj_cert_id, a.remind_time, a.remind_type
    from rv_authprj_cert_remind a
    join rv_authprj_cert        b on a.authprj_cert_id = b.id and a.org_id = b.org_id and b.deleted = 0
    join rv_authprj             c on b.authprj_id = c.id and b.org_id = c.org_id and c.deleted = 0
    join rv_activity            d on c.aom_prj_id = d.id and c.org_id = d.org_id and d.deleted = 0
    where a.deleted = 0
      and d.actv_status = 2 -- 只查询进行中的认证项目
    order by a.org_id, c.id, a.authprj_cert_id, a.remind_time
  </select>
</mapper>