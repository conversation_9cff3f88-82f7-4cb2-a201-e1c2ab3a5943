package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom;

import com.yxt.talent.rv.application.aom.dto.ActivityObjectiveResultWithActivityInfoDTO;
import com.yxt.talent.rv.application.aom.dto.IndicatorLatestActivityDTO;
import com.yxt.talent.rv.application.authprj.dto.UserRefActiveResultDTO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityObjectiveResultPO;
import com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 【注意】来自aom定义的表，只应用于查询，禁止修改
 */
public interface RvActivityObjectiveResultMapper extends CommonMapper<ActivityObjectiveResultPO> {

    int insert(ActivityObjectiveResultPO record);

    ActivityObjectiveResultPO selectByPrimaryKey(Long id);

    int updateByPrimaryKey(ActivityObjectiveResultPO record);

    int updateBatch(@Param("list") List<ActivityObjectiveResultPO> list);

    int batchInsert(@Param("list") List<ActivityObjectiveResultPO> list);

    List<XpdIndicatorResultDto> queryByUserIds(
        @Param("orgId") String orgId,
        @Param("actvIds") Collection<String> actvIds,
        @Param("userIds") Collection<String> userIds,
        @Param("objectiveIds") Collection<String> objectiveIds);

    /**
     * 查询指定活动和用户的所有指标结果
     */
    List<ActivityObjectiveResultPO> selectByActvIdAndUserId(
        @Param("orgId") String orgId,
        @Param("actvId") String actvId,
        @Param("userId") String userId);

    /**
     * 根据AOM项目ID查询用户的最新活动指标结果
     * 对于每个指标，选择包含该指标的最新活动，然后查询用户在该活动中的结果
     * 如果用户在某个指标的最新活动中没有结果，该指标不会出现在查询结果中
     */
    List<ActivityObjectiveResultWithActivityInfoDTO> selectLatestIndicatorResultsByAomProjectId(
        @Param("orgId") String orgId,
        @Param("aomProjectId") String aomProjectId,
        @Param("userId") String userId);

    /**
     * 查询AOM项目中所有指标的最新活动信息
     * 用于识别哪些指标在最新活动中没有用户结果，需要清空数据
     */
    List<IndicatorLatestActivityDTO> selectIndicatorLatestActivitiesByAomProjectId(
        @Param("orgId") String orgId,
        @Param("aomProjectId") String aomProjectId);

    /**
     * 查询指定活动和用户的所有指标结果
     * @param orgId
     * @param actvId
     * @param refId
     * @param userId
     * @return
     */
    List<UserRefActiveResultDTO> selectByActvIdAndRefIdAndUserId(
        @Param("orgId") String orgId, @Param("actvId") String actvId, @Param("refId") String refId,
        @Param("userId") String userId);
}