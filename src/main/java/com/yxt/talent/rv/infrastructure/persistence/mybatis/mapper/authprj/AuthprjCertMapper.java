package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.AuthPrjCertVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjCertPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuthprjCertMapper extends CommonMapper<AuthprjCertPO>, BaseMapper<AuthprjCertPO> {
    int batchInsertOrUpdate(@Param("list") List<AuthprjCertPO> list);

    /**
     * 根据认证项目ID查询证书列表（包含证书模板信息）
     */
    List<AuthPrjCertVO> selectCertListByAuthprjId(@Param("orgId") String orgId, @Param("authprjId") String authprjId);

    /**
     * 统计指定机构+项目下，某证书模板已配置的证书数量（未删除）
     */
    int countByOrgAuthprjAndCertTempId(@Param("orgId") String orgId,
                                       @Param("authprjId") String authprjId,
                                       @Param("certTempId") String certTempId);

    /**
     * 统计指定机构+项目下，某证书模板已配置的证书数量（未删除，排除指定证书ID）
     */
    int countByOrgAuthprjAndCertTempIdExcludeId(@Param("orgId") String orgId,
                                                @Param("authprjId") String authprjId,
                                                @Param("certTempId") String certTempId,
                                                @Param("excludeId") String excludeId);
}