package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserIndicatorPO;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface AuthprjResultUserIndicatorMapper extends CommonMapper<AuthprjResultUserIndicatorPO>, BaseMapper<AuthprjResultUserIndicatorPO> {
    int batchInsertOrUpdate(@Param("list") List<AuthprjResultUserIndicatorPO> list);

    List<AuthprjResultUserIndicatorPO> selectByActvRefIdAndUserId(
        @Param("orgId") String orgId, @Param("authprjId") String authprjId, @Param("sourceId") String sourceId,
        @Param("userId") String userId);

    /**
     * 查询用户在指定认证项目下的所有指标结果
     */
    List<AuthprjResultUserIndicatorPO> selectByAuthprjIdAndUserId(
        @Param("orgId") String orgId, @Param("authprjId") String authprjId, @Param("userId") String userId);

    List<AuthprjResultUserIndicatorPO> selectByActvRefIdAndUserIds(@Param("orgId") String orgId, @Param("authprjId") String authprjId, @Param("userIds") List<String> userIds);

    void clearUserRecord(@Param("orgId") String orgId, @Param("authPrjId") String authPrjId, @Param("userIds") Collection<String> userIds);
    
    /**
     * 根据认证项目ID和用户ID删除指标结果
     *
     * @param orgId     机构ID
     * @param authPrjId 认证项目ID
     * @param userId    用户ID
     */
    int deleteByAuthPrjAndUser(@Param("orgId") String orgId, @Param("authPrjId") String authPrjId, @Param("userId") String userId);

    int deleteByUserIdAndIndicatorIds(
        @Param("orgId") String orgId, @Param("authPrjId") String authPrjId, @Param("userId") String userId,
        @Param("indicatorIds") Set<String> indicatorIds);
}