package com.yxt.talent.rv.application.authprj;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.cerapifacade.bean.IssueListSearchConditionReq;
import com.yxt.cerapifacade.bean.IssueListSearchConditionResult;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.activity.dto.UserProjectDTO;
import com.yxt.talent.rv.application.authprj.AuthPrjIndicatorHierarchyService.IndicatorDataConverter;
import com.yxt.talent.rv.application.authprj.AuthPrjIndicatorHierarchyService.IndicatorProcessContext;
import com.yxt.talent.rv.application.authprj.dto.AuthPrjChangeUserResDTO;
import com.yxt.talent.rv.application.authprj.dto.AuthPrjDimGroupDTO;
import com.yxt.talent.rv.application.authprj.dto.AuthPrjIndicatorDetailDTO;
import com.yxt.talent.rv.application.authprj.dto.UserResultDTO;
import com.yxt.talent.rv.controller.client.authprj.query.PrjListQuery;
import com.yxt.talent.rv.controller.manage.authprj.viewobj.*;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityParticipationMemberMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.remote.CertAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static java.util.Collections.singletonList;


/**
 * 认证项目员工服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthPrjUserAppService {

    private final AuthPrjUserOverviewMapper authprjUserOverviewMapper;
    private final AuthPrjIndicatorHierarchyService hierarchyService;
    private final Executor wafTaskExecutor;
    private final AuthprjResultUserMapper authprjResultUserMapper;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final AuthprjResultUserHistoryMapper authprjResultUserHistoryMapper;
    private final AuthprjResultUserChglogMapper authprjResultUserChglogMapper;
    private final AuthprjRuleLevelMapper authprjRuleLevelMapper;
    private final RvActivityParticipationMemberMapper rvActivityParticipationMemberMapper;
    private final AuthPrjUserExportService authPrjUserExportService;
    private final I18nComponent i18nComponent;
    private final AuthPrjCertIssueService authPrjCertIssueService;
    private final CertAclService certAclService;
    private final AuthprjMapper authprjMapper;

    /**
     * 获取员工统计数据
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @param userId    用户ID
     * @return 统计数据列表
     */
    public AuthPrjUserUserStatisticsVO getUserStatistics(String orgId, String authprjId, String userId) {
        CompletableFuture<BigDecimal> totalScoreFuture = CompletableFuture.supplyAsync(
            () -> authprjUserOverviewMapper.calculateUserTotalScore(orgId, authprjId, userId)
        , wafTaskExecutor);

        CompletableFuture<BigDecimal> progressFuture = CompletableFuture.supplyAsync(
            () -> authprjUserOverviewMapper.calculateUserProgress(orgId, authprjId, userId)
        , wafTaskExecutor);

        CompletableFuture<String> levelNameFuture = CompletableFuture.supplyAsync(
            () -> authprjUserOverviewMapper.getUserLevelName(orgId, authprjId, userId)
        , wafTaskExecutor);

        // 证书数量改为远程实时查询
        CompletableFuture<Integer> cerCountFuture = CompletableFuture.supplyAsync(
            () -> countUserCertificatesRemote(orgId, authprjId, userId)
        , wafTaskExecutor);

        return new AuthPrjUserUserStatisticsVO(
            cerCountFuture.join(), progressFuture.join(), totalScoreFuture.join(), levelNameFuture.join());
    }

    /**
     * 使用证书远程服务实时统计用户在某认证项目下获得的证书数量
     */
    private int countUserCertificatesRemote(String orgId, String authprjId, String userId) {
        try {
            AuthprjPO authprjPO = authprjMapper.selectByOrgIdAndId(orgId, authprjId);
            if (authprjPO == null) {
                return 0;
            }

            IssueListSearchConditionReq req = buildIssueListSearchConditionReq(orgId, userId, authprjPO);

            PagingList<IssueListSearchConditionResult> result = certAclService.getIssueList(req);
            if (result == null || result.getDatas() == null) {
                return 0;
            }
            return result.getDatas().size();
        } catch (Exception e) {
            log.error("实时统计用户证书数量失败: orgId={}, authprjId={}, userId={}, error={}", orgId, authprjId, userId, e.getMessage(), e);
            return 0;
        }
    }

    @NotNull
    private static IssueListSearchConditionReq buildIssueListSearchConditionReq(
        String orgId, String userId,
        AuthprjPO authprjPO) {
        IssueListSearchConditionReq req = new IssueListSearchConditionReq();
        req.setOrgId(orgId);
        req.setSourceId(authprjPO.getAomPrjId());
        req.setSourceType(AppConstants.AUTHPRJ_CERT_SOURCE_FLAG); // 1-项目发证
        req.setUserIds(Collections.singletonList(userId));
        // 只统计已颁发的有效证书
        req.setStatusList(singletonList(1));
        // 请求尽可能大的分页，直接以返回数量为准，避免依赖 Paging 的 total 字段
        req.setCurrent(1);
        req.setLimit(Integer.MAX_VALUE);
        return req;
    }

    /**
     * 获取员工认证指标结果明细
     *
     * @param orgId     机构ID
     * @param authprjId 认证项目ID
     * @param userId    用户ID
     * @return 认证指标结果明细列表
     */
    public List<AuthPrjUserReportVO> getUserIndicatorDetails(String orgId, String authprjId, String userId) {
        log.info("LOG10256:获取员工认证指标结果明细: orgId={}, authprjId={}, userId={}", orgId, authprjId, userId);

        // 初始化指标处理上下文
        IndicatorProcessContext context = hierarchyService.initProcessContext(orgId, authprjId);
        if (context == null) {
            return new ArrayList<>();
        }

        // 获取员工指标详细数据
        List<AuthPrjUserIndicatorDetailVO> indicatorDetails =
            authprjResultUserMapper.getUserIndicatorDetails(orgId, authprjId, userId, new ArrayList<>(context.getIndicatorIds()));

        // 按维度分组
        List<AuthPrjUserReportVO> result = groupUserIndicatorsByDimension(context, indicatorDetails);
        log.info("员工认证指标结果明细获取成功: orgId={}, authprjId={}, userId={}, 维度分组数量={}",
                orgId, authprjId, userId, result.size());
        return result;
    }

    /**
     * 按维度分组员工指标数据
     */
    private List<AuthPrjUserReportVO> groupUserIndicatorsByDimension(
            IndicatorProcessContext context, List<AuthPrjUserIndicatorDetailVO> indicatorDetails) {

        // 创建数据转换器
        IndicatorDataConverter<AuthPrjUserIndicatorDetailVO> converter = new IndicatorDataConverter<AuthPrjUserIndicatorDetailVO>() {
            @Override
            public AuthPrjIndicatorDetailDTO convertToDTO(AuthPrjUserIndicatorDetailVO source) {
                AuthPrjIndicatorDetailDTO dto = new AuthPrjIndicatorDetailDTO();
                BeanUtils.copyProperties(source, dto);
                return dto;
            }

            @Override
            public String getOriginalIndicatorId(AuthPrjUserIndicatorDetailVO source) {
                return source.getFirstIndicatorId();
            }
        };

        // 使用通用方法进行分组处理
        List<AuthPrjDimGroupDTO> commonResult = hierarchyService.groupIndicatorsByDimension(context, indicatorDetails, converter);

        // 转换为特定的返回类型
        return commonResult.stream().map(this::convertToUserReportVO).collect(Collectors.toList());
    }

    /**
     * 将通用DTO转换为用户报告VO
     */
    private AuthPrjUserReportVO convertToUserReportVO(AuthPrjDimGroupDTO dto) {
        AuthPrjUserReportVO vo = new AuthPrjUserReportVO();
        vo.setSdDimId(dto.getSdDimId());
        vo.setSdDimName(dto.getSdDimName());

        // 转换指标列表
        List<AuthPrjUserIndicatorDetailVO> indicatorList = dto.getIndicatorList().stream()
                .map(this::convertToUserIndicatorDetailVO)
                .collect(Collectors.toList());
        vo.setIndicatorList(indicatorList);

        return vo;
    }

    /**
     * 将通用指标DTO转换为用户指标详细VO
     */
    private AuthPrjUserIndicatorDetailVO convertToUserIndicatorDetailVO(AuthPrjIndicatorDetailDTO dto) {
        AuthPrjUserIndicatorDetailVO vo = new AuthPrjUserIndicatorDetailVO();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    public AuthPrjUserHistoryVO getUserHistory(String orgId, String authPrjId, String userId) {
        // 获取用户信息
        UdpLiteUserPO udpLiteUserPO = udpLiteUserMapper.selectByUserId(orgId, userId);
        if (udpLiteUserPO == null) {
            throw new ApiException(ExceptionKeys.USER_NOT_EXISTED);
        }
        AuthPrjUserHistoryVO authPrjUserHistoryVO = new AuthPrjUserHistoryVO();
        BeanHelper.copyProperties(udpLiteUserPO, authPrjUserHistoryVO);

        // 获取历史记录
        List<AuthPrjHistoryRecordVO> recordVOList = new ArrayList<>();
        int order = 1;
        // 获取历史记录列表
        List<AuthprjResultUserHistoryPO> authprjResultUserHistoryPOS = authprjResultUserHistoryMapper.selectByAuthprjIdAndUserId(orgId, authPrjId, userId);
        if (CollectionUtils.isNotEmpty(authprjResultUserHistoryPOS)) {
            for (AuthprjResultUserHistoryPO authprjResultUserHistoryPO : authprjResultUserHistoryPOS) {
                AuthPrjHistoryRecordVO authRecord = new AuthPrjHistoryRecordVO();
                authRecord.setAuthTime(authprjResultUserHistoryPO.getAuthTime());
                authRecord.setOrder(order);
                authRecord.setLeveId(authprjResultUserHistoryPO.getLevelId());
                recordVOList.add(authRecord);
                order++;
            }
        }

        // 获取当前最新一条记录
        AuthprjResultUserPO authprjResultUserPO = authprjResultUserMapper.selectByAuthprjIdAndUserId(orgId, authPrjId, userId);
        if (authprjResultUserPO != null && StringUtils.isNotBlank(authprjResultUserPO.getLevelId())) {
            AuthPrjHistoryRecordVO authPrjHistoryRecordVO = new AuthPrjHistoryRecordVO();
            authPrjHistoryRecordVO.setAuthTime(authprjResultUserPO.getAuthTime());
            authPrjHistoryRecordVO.setOrder(order);
            authPrjHistoryRecordVO.setLeveId(authprjResultUserPO.getLevelId());
            recordVOList.add(authPrjHistoryRecordVO);
        }

        if (CollectionUtils.isNotEmpty(recordVOList)){
            recordVOList.forEach(authPrjHistoryRecordVO -> {
                Map<String, AuthprjRuleLevelPO> levalMap = getLevalMap(orgId, authPrjId);
                if (levalMap.containsKey(authPrjHistoryRecordVO.getLeveId())){
                    authPrjHistoryRecordVO.setAuthResult(levalMap.get(authPrjHistoryRecordVO.getLeveId()).getLevelName());
                }
            });
            authPrjUserHistoryVO.setHistoryRecord(recordVOList);
        }
        return authPrjUserHistoryVO;
    }

    public AuthPrjUserResultVO getUserAuthResult(String orgId, String authPrjId, String userId) {
        UdpLiteUserPO udpLiteUserPO = udpLiteUserMapper.selectByUserId(orgId, userId);
        if (udpLiteUserPO == null) {
            throw new ApiException(ExceptionKeys.USER_NOT_EXISTED);
        }
        AuthPrjUserResultVO authPrjUserResultVO = new AuthPrjUserResultVO();
        BeanHelper.copyProperties(udpLiteUserPO, authPrjUserResultVO);

        AuthprjResultUserPO authprjResultUserPO = authprjResultUserMapper.selectByAuthprjIdAndUserId(orgId, authPrjId, userId);
        if (authprjResultUserPO != null) {
            Map<String, AuthprjRuleLevelPO> levalMap = getLevalMap(orgId, authPrjId);
            authPrjUserResultVO.setScore(authprjResultUserPO.getScoreValue());
            AuthprjRuleLevelPO authprjRuleLevelPO = levalMap.get(authprjResultUserPO.getLevelId());
            if (authprjRuleLevelPO != null){
                IdName idName = new IdName();
                idName.setId(authprjRuleLevelPO.getId());
                idName.setName(authprjRuleLevelPO.getLevelName());
                authPrjUserResultVO.setLevel(idName);
            }
        }
        return authPrjUserResultVO;
    }

    public void changeAuthResult(String orgId, String opUserId, AuthPrjChangeUserResDTO authPrjChangeUserResDTO) {
        String authPrjId = authPrjChangeUserResDTO.getAuthPrjId();
        String userId = authPrjChangeUserResDTO.getUserId();
        // 添加历史记录
        AuthprjResultUserPO authprjResultUserPO = authprjResultUserMapper.selectByAuthprjIdAndUserId(orgId, authPrjId, userId);
        if (authprjResultUserPO == null) {
            throw new ApiException(ExceptionKeys.AUTHPRJ_RESULT_NOT_EXIST);
        }
        // 修改记录添加
        AuthprjResultUserChglogPO authprjResultUserChglogPO = new AuthprjResultUserChglogPO();
        BeanHelper.copyProperties(authPrjChangeUserResDTO, authprjResultUserChglogPO);
        authprjResultUserChglogPO.setId(ApiUtil.getUuid());
        authprjResultUserChglogPO.setOrgId(orgId);
        authprjResultUserChglogPO.setAuthprjId(authPrjId);
        authprjResultUserChglogPO.setOldLevelId(authprjResultUserPO.getLevelId());
        authprjResultUserChglogPO.setOldScoreValue(authprjResultUserPO.getScoreValue());
        authprjResultUserChglogPO.setLevelId(authPrjChangeUserResDTO.getLevelId());
        authprjResultUserChglogPO.setScoreValue(authPrjChangeUserResDTO.getScoreValue());
        authprjResultUserChglogPO.setUpdateUserId(opUserId);
        authprjResultUserChglogPO.setUpdateTime(LocalDateTime.now());
        authprjResultUserChglogPO.setCreateUserId(opUserId);
        authprjResultUserChglogPO.setCreateTime(LocalDateTime.now());
        authprjResultUserChglogPO.setReason(authPrjChangeUserResDTO.getReason());
        authprjResultUserChglogMapper.insert(authprjResultUserChglogPO);

        // 源数据修改
        authprjResultUserPO.setLevelId(authPrjChangeUserResDTO.getLevelId());
        authprjResultUserPO.setScoreValue(authPrjChangeUserResDTO.getScoreValue());
        authprjResultUserPO.setManualFlag(1);
        authprjResultUserPO.setUpdateUserId(opUserId);
        authprjResultUserPO.setUpdateTime(LocalDateTime.now());
        // 修改记录
        authprjResultUserMapper.updateById(authprjResultUserPO);

        // 手动修改认证结果需要触发计算颁发证书的逻辑
        authPrjCertIssueService.checkAndIssueCerts(orgId, authPrjId, userId);
    }


    /**
     * 获取认证项目分层结果Map
     * @param orgId
     * @param authPrjId
     * @return
     */
    public Map<String, AuthprjRuleLevelPO> getLevalMap(String orgId, String authPrjId){
        List<AuthprjRuleLevelPO> authprjRuleLevelPOS = authprjRuleLevelMapper.selectByAuthprjId(orgId, authPrjId);
        if (CollectionUtils.isEmpty(authprjRuleLevelPOS)){
            return new HashMap<>();
        }
        return StreamUtil.list2map(authprjRuleLevelPOS, AuthprjRuleLevelPO::getId);
    }

    public List<AuthPrjUserChgLogVO> getChgLog(String orgId, String authPrjId, String userId) {
        List<AuthprjResultUserChglogPO> authprjResultUserChglogPOS = authprjResultUserChglogMapper.selectByAuthprjIdAndUserId(orgId, authPrjId, userId);
        List<AuthPrjUserChgLogVO> recordVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(authprjResultUserChglogPOS)) {
            for (AuthprjResultUserChglogPO authprjResultUserChglogPO : authprjResultUserChglogPOS) {
                AuthPrjUserChgLogVO authRecord = new AuthPrjUserChgLogVO();
                BeanHelper.copyProperties(authprjResultUserChglogPO, authRecord);
                authRecord.setLeveId(authprjResultUserChglogPO.getOldLevelId());
                authRecord.setScoreValue(authprjResultUserChglogPO.getOldScoreValue());
                recordVOList.add(authRecord);
            }
        }

        if (CollectionUtils.isNotEmpty(recordVOList)){
            Map<String, AuthprjRuleLevelPO> levalMap = getLevalMap(orgId, authPrjId);
            recordVOList.forEach(authPrjUserChgLogVO -> {
                if (levalMap.containsKey(authPrjUserChgLogVO.getLeveId())){
                    authPrjUserChgLogVO.setAuthResult(levalMap.get(authPrjUserChgLogVO.getLeveId()).getLevelName());
                }
            });
        }
        return recordVOList;
    }

    /**
     * 获取用户分层结果
     * @param orgId
     * @param authprjId
     * @param userIds
     * @return
     */
    public Map<String, UserResultDTO> getUserResult(String orgId, String authprjId, List<String> userIds) {
        List<AuthprjResultUserPO> authprjResultUserPOs = authprjResultUserMapper.selectByAuthprjIdAndUserIds(orgId, authprjId, userIds);
        if (CollectionUtils.isEmpty(authprjResultUserPOs)){
            return new HashMap<>();
        }
        Map<String, UserResultDTO> userMap = new HashMap<>();
        Map<String, AuthprjRuleLevelPO> levalMap = getLevalMap(orgId, authprjId);
        for (AuthprjResultUserPO authprjResultUserPO : authprjResultUserPOs) {
            AuthprjRuleLevelPO authprjRuleLevelPO = levalMap.get(authprjResultUserPO.getLevelId());
            UserResultDTO userResult = new UserResultDTO();
            if (authprjRuleLevelPO != null){
                userResult.setLevelName(authprjRuleLevelPO.getLevelName());
                userResult.setLevelId(authprjRuleLevelPO.getId());
            }
            userResult.setScore(authprjResultUserPO.getScoreValue());
            userMap.put(authprjResultUserPO.getUserId(), userResult);
        }
        return userMap;
    }

    public PagingList<UserProjectDTO> getProjectList(String orgId, String userId, PrjListQuery prjListQuery, PageRequest pageRequest) {
        IPage<UserProjectDTO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        if (StringUtils.isNotBlank(prjListQuery.getKeyword())) {
            prjListQuery.setKeyword(SqlUtil.escapeSql(prjListQuery.getKeyword()));
        }
        IPage<UserProjectDTO> list = rvActivityParticipationMemberMapper.getUserProjectPage(page, orgId, userId, prjListQuery);
        return BeanCopierUtil.toPagingList(list, UserProjectDTO.class, UserProjectDTO.class);
    }

    public void getImportUserTemp(String orgId, String optUserId) {
        String fileName = i18nComponent.getI18nValue("apis.authprj.user.import.tem.file.name");
        String fullname = "";
        UdpLiteUserPO user = udpLiteUserMapper.selectById(optUserId);
        if (user != null) {
            fullname = user.getFullname();
        }
        authPrjUserExportService.exportFile(orgId, optUserId, fileName, fullname);
    }

}
