package com.yxt.talent.rv.application.aom.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 指标最新活动信息DTO
 * 用于表示每个指标所在的最新活动信息
 */
@Data
public class IndicatorLatestActivityDTO {

    /**
     * 指标ID
     */
    private String objectiveId;

    /**
     * 最新活动的编排项ID
     */
    private String latestItemId;

    /**
     * 最新活动的编排项名称
     */
    private String latestItemName;

    /**
     * 最新活动的创建时间
     */
    private LocalDateTime latestCreateTime;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;
}
