package com.yxt.talent.rv.application.authprj;

import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.spsdfacade.bean.spsd.ModelInfo;
import com.yxt.spsdfacade.bean.spsd.ModelRequireBaseInfo;
import com.yxt.spsdfacade.bean.spsd.ModelRequireDetailInfo;
import com.yxt.spsdk.common.utils.YxtBasicUtils;
import com.yxt.talent.rv.application.aom.dto.ActivityObjectiveResultWithActivityInfoDTO;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityObjectiveResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjResultUserIndicatorMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjResultUserIndicatorPO;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

/**
 * 认证项目指标结果处理服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthPrjIndicatorResultService {

    private final AuthprjResultUserIndicatorMapper authprjResultUserIndicatorMapper;
    private final RvActivityObjectiveResultMapper rvActivityObjectiveResultMapper;
    private final AuthprjMapper authprjMapper;
    private final SpsdAclService spsdAclService;

    /**
     * 处理用户指标结果
     * 从AOM的活动指标结果表中查询数据，处理去重逻辑后保存到认证项目指标结果表
     * 【注意】只有活动指标与认证项目模型指标的交集部分才会被保存
     *
     * @param orgId     机构ID
     * @param authPrjId 认证项目ID
     * @param userId    用户ID
     * @param actvId    活动ID
     */
    public void processUserIndicatorResults(String orgId, String authPrjId, String userId, String actvId) {
        try {
            log.info("LOG10017:开始处理用户指标结果, authPrjId={}, userId={}, actvId={}", authPrjId, userId, actvId);

            // 1. 从AOM活动指标结果表查询用户的最新指标结果（SQL中已完成去重）
            List<ActivityObjectiveResultWithActivityInfoDTO> aomResults = queryAomObjectiveResults(orgId, userId, actvId);
            if (CollectionUtils.isEmpty(aomResults)) {
                log.debug(
                    "LOG10031:未找到AOM指标结果数据, authPrjId={}, userId={}, actvId={}", authPrjId, userId,
                    actvId);
                return;
            }

            // 2. 获取指标交集，过滤无关指标
            // 【注意】这里与盘点不一样的地方：活动（尤其是考试活动,它们可以使用任意模型）上报的指标可能和认证项目下指定的模型中的指标完全不一样，而概览和个人报告又要求只展示模型中的指标，所以需要取个交集
            Set<String> validIndicatorIds = getValidIndicatorIds(orgId, authPrjId, aomResults);
            if (validIndicatorIds.isEmpty()) {
                log.warn("LOG10037:活动指标与模型指标无交集, authPrjId={}, userId={}, actvId={}",
                        authPrjId, userId, actvId);
                return;
            }

            // 3. 过滤AOM结果，只保留交集中的指标
            List<ActivityObjectiveResultWithActivityInfoDTO> filteredAomResults = aomResults.stream()
                .filter(result -> validIndicatorIds.contains(result.getObjectiveId()))
                .collect(Collectors.toList());

            if (filteredAomResults.isEmpty()) {
                log.warn("LOG10038:过滤后无有效指标结果, authPrjId={}, userId={}, actvId={}",
                        authPrjId, userId, actvId);
                return;
            }

            log.info("LOG10039:指标过滤完成, authPrjId={}, userId={}, 原始指标数={}, 有效指标数={}",
                    authPrjId, userId, aomResults.size(), filteredAomResults.size());

            // 4. 查询现有的认证项目指标结果
            List<AuthprjResultUserIndicatorPO> existingResults = queryExistingIndicatorResults(orgId, authPrjId, userId);

            // 5. 直接转换为保存格式（无需去重，SQL中已处理）
            List<AuthprjResultUserIndicatorPO> toSaveResults =
                convertToSaveResults(orgId, authPrjId, userId, filteredAomResults, existingResults);

            // 6. 批量保存指标结果
            if (!toSaveResults.isEmpty()) {
                BatchOperationUtil.batchExecute(toSaveResults, 200,
                    authprjResultUserIndicatorMapper::batchInsertOrUpdate);
                log.info(
                    "LOG67630:保存指标结果完成, authPrjId={}, userId={}, count={}", authPrjId, userId,
                    toSaveResults.size());
            }
        } catch (Exception e) {
            log.error(
                "LOG10033:处理用户指标结果失败, authPrjId={}, userId={}, actvId={}, error={}", authPrjId, userId,
                actvId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 清理指定用户的认证项目指标结果
     *
     * @param orgId     机构ID
     * @param authPrjId 认证项目ID
     * @param userId    用户ID
     */
    public void deleteUserIndicatorResults(String orgId, String authPrjId, String userId) {
        authprjResultUserIndicatorMapper.deleteByAuthPrjAndUser(orgId, authPrjId, userId);
        log.info("LOG41116:已清理用户认证项目历史指标结果, authPrjId={}, userId={}", authPrjId, userId);
    }

    /**
     * 查询AOM活动指标结果
     * 直接获取用户每个指标的最新活动结果，SQL中已完成去重逻辑
     */
    private List<ActivityObjectiveResultWithActivityInfoDTO> queryAomObjectiveResults(
        String orgId, String userId, String actvId) {
        try {
            // 使用高效查询方法，SQL中已完成去重，直接返回每个指标的最新结果
            List<ActivityObjectiveResultWithActivityInfoDTO> results =
                rvActivityObjectiveResultMapper.selectLatestIndicatorResultsByAomProjectId(orgId, actvId, userId);

            log.debug(
                "LOG10034:查询AOM最新指标结果成功, orgId={}, userId={}, actvId={}, count={}", orgId, userId,
                actvId, results.size());

            return results;
        } catch (Exception e) {
            log.error(
                "LOG67640:查询AOM最新指标结果失败, orgId={}, userId={}, actvId={}, error={}", orgId, userId,
                actvId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 查询现有的认证项目指标结果
     */
    private List<AuthprjResultUserIndicatorPO> queryExistingIndicatorResults(
        String orgId, String authPrjId, String userId) {
        try {
            // 使用新增的查询方法，查询该用户在该认证项目下的所有指标结果
            List<AuthprjResultUserIndicatorPO> results =
                authprjResultUserIndicatorMapper.selectByAuthprjIdAndUserId(orgId, authPrjId, userId);

            log.debug(
                "LOG10035:查询现有指标结果成功, authPrjId={}, userId={}, count={}", authPrjId, userId,
                results.size());

            return results;
        } catch (Exception e) {
            log.error(
                "LOG67650:查询现有指标结果失败, authPrjId={}, userId={}, error={}", authPrjId, userId,
                e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 转换为保存格式
     * SQL中已完成去重，这里只需要简单转换格式
     */
    private List<AuthprjResultUserIndicatorPO> convertToSaveResults(
        String orgId, String authPrjId, String userId,
        List<ActivityObjectiveResultWithActivityInfoDTO> aomResults,
        List<AuthprjResultUserIndicatorPO> existingResults) {

        try {
            List<AuthprjResultUserIndicatorPO> toSaveList = new ArrayList<>();
            String operator = YxtBasicUtils.userInfo().getUserId();
            operator = StringUtils.isBlank(operator) ? AppConstants.CODE_OPERATOR_ID : operator;
            LocalDateTime now = LocalDateTime.now();

            // 将现有结果按指标ID分组
            Map<String, AuthprjResultUserIndicatorPO> existingMap = existingResults.stream()
                .collect(Collectors.toMap(AuthprjResultUserIndicatorPO::getSdIndicatorId, p -> p, (p1, p2) -> p1));

            for (ActivityObjectiveResultWithActivityInfoDTO aomResult : aomResults) {
                String indicatorId = aomResult.getObjectiveId();

                // 直接转换，无需时间比较（SQL中已完成去重）
                AuthprjResultUserIndicatorPO existing = existingMap.get(indicatorId);
                AuthprjResultUserIndicatorPO indicatorPO = new AuthprjResultUserIndicatorPO();
                indicatorPO.setId(existing != null ? existing.getId() : ApiUtil.getUuid());
                indicatorPO.setOrgId(orgId);
                indicatorPO.setAuthprjId(authPrjId);
                indicatorPO.setUserId(userId);
                indicatorPO.setSdIndicatorId(indicatorId);
                indicatorPO.setSourceId(aomResult.getActvId()); // 使用外部真实活动ID作为来源
                indicatorPO.setScoreTotal(aomResult.getObjectiveTotalScore());
                indicatorPO.setScore(aomResult.getObjectiveScore());
                indicatorPO.setDeleted(0);
                indicatorPO.setCreateUserId(operator);
                indicatorPO.setCreateTime(existing != null ? existing.getCreateTime() : now);
                indicatorPO.setUpdateUserId(operator);
                indicatorPO.setUpdateTime(now);

                toSaveList.add(indicatorPO);
            }

            return toSaveList;

        } catch (Exception e) {
            log.error(
                "LOG67660:转换指标结果格式失败, authPrjId={}, userId={}, error={}", authPrjId, userId,
                e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取有效的指标ID集合（活动指标与模型指标的交集）
     * 参照AuthPrjIndicatorHierarchyService.initProcessContext的逻辑
     *
     * @param orgId     机构ID
     * @param authPrjId 认证项目ID
     * @param aomResults AOM活动指标结果
     * @return 有效指标ID集合
     */
    private Set<String> getValidIndicatorIds(String orgId, String authPrjId, List<ActivityObjectiveResultWithActivityInfoDTO> aomResults) {
        try {
            // 1. 获取认证项目信息
            AuthprjPO authprj = authprjMapper.selectByIdAndOrg(orgId, authPrjId);
            if (authprj == null) {
                log.warn("认证项目不存在: orgId={}, authPrjId={}", orgId, authPrjId);
                return new HashSet<>();
            }

            // 2. 收集活动中的指标ID
            Set<String> activityIndicatorIds = collectActivityIndicatorIds(aomResults);
            if (activityIndicatorIds.isEmpty()) {
                log.warn("AOM结果中没有指标信息: orgId={}, authPrjId={}", orgId, authPrjId);
                return new HashSet<>();
            }

            // 3. 获取模型信息
            ModelInfo spsdModelInfo = spsdAclService.getModelInfo(orgId, authprj.getModelId());
            if (spsdModelInfo == null || CollectionUtils.isEmpty(spsdModelInfo.getDms())) {
                log.warn("模型信息为空: orgId={}, modelId={}", orgId, authprj.getModelId());
                return new HashSet<>();
            }

            // 4. 直接使用模型信息
            // 5. 收集模型中定义的所有指标ID
            Set<String> modelIndicatorIds = collectModelIndicatorIds(spsdModelInfo.getDms());
            if (modelIndicatorIds.isEmpty()) {
                log.warn("模型中没有定义指标: orgId={}, modelId={}", orgId, authprj.getModelId());
                return new HashSet<>();
            }

            // 6. 计算活动指标与模型指标的交集
            Set<String> validIndicatorIds = intersectIndicatorIds(activityIndicatorIds, modelIndicatorIds);
            
            log.info(
                "LOG42016:指标交集处理完成: orgId={}, authPrjId={}, 活动指标数={}, 模型指标数={}, 交集指标数={}",
                    orgId, authPrjId, activityIndicatorIds.size(), modelIndicatorIds.size(), validIndicatorIds.size());

            return validIndicatorIds;

        } catch (Exception e) {
            log.error("获取有效指标ID失败: orgId={}, authPrjId={}, error={}", orgId, authPrjId, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 从AOM结果中收集活动指标ID
     */
    private Set<String> collectActivityIndicatorIds(List<ActivityObjectiveResultWithActivityInfoDTO> aomResults) {
        Set<String> indicatorIds = new HashSet<>();
        for (ActivityObjectiveResultWithActivityInfoDTO result : aomResults) {
            if (StringUtils.isNotBlank(result.getObjectiveId())) {
                indicatorIds.add(result.getObjectiveId());
            }
        }
        return indicatorIds;
    }

    /**
     * 收集模型中定义的所有指标ID
     * 参照AuthPrjIndicatorHierarchyService.collectModelIndicatorIds的逻辑
     */
    private Set<String> collectModelIndicatorIds(List<ModelRequireBaseInfo> dms) {
        Set<String> indicatorIds = new HashSet<>();
        if (CollectionUtils.isEmpty(dms)) {
            return indicatorIds;
        }

        for (ModelRequireBaseInfo dm : dms) {
            if (CollectionUtils.isNotEmpty(dm.getDetails())) {
                for (ModelRequireDetailInfo detail : dm.getDetails()) {
                    if (StringUtils.isNotBlank(detail.getItemId())) {
                        indicatorIds.add(detail.getItemId());
                    }
                    if(isNotEmpty(detail.getChilds())) {
                        indicatorIds.addAll(collectSubIndicatorIds(detail.getChilds()));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(dm.getChilds())) {
                indicatorIds.addAll(collectModelIndicatorIds(dm.getChilds()));
            }
        }
        return indicatorIds;
    }

    private Collection<String> collectSubIndicatorIds(List<ModelRequireDetailInfo> children) {
        Set<String> indicatorIds = new HashSet<>();
        if (CollectionUtils.isEmpty(children)) {
            return indicatorIds;
        }

        for (ModelRequireDetailInfo child : children) {
            if (StringUtils.isNotBlank(child.getItemId())) {
                indicatorIds.add(child.getItemId());
            }
            if(isNotEmpty(child.getChilds())) {
                indicatorIds.addAll(collectSubIndicatorIds(child.getChilds()));
            }
        }
        return indicatorIds;
    }

    /**
     * 计算两个指标集合的交集
     * 参照AuthPrjIndicatorHierarchyService.intersectIndicatorIds的逻辑
     */
    private Set<String> intersectIndicatorIds(Set<String> indicatorIds1, Set<String> indicatorIds2) {
        Set<String> intersection = new HashSet<>(indicatorIds1);
        intersection.retainAll(indicatorIds2);
        return intersection;
    }

    /**
     * 计算用户总得分
     */
    public BigDecimal calculateUserTotalScore(String orgId, String authprjId, String userId) {
        try {
            // 查询该用户在该认证项目下的所有指标得分，计算总分
            List<AuthprjResultUserIndicatorPO> indicators =
                authprjResultUserIndicatorMapper.selectByAuthprjIdAndUserId(orgId, authprjId, userId);

            if (CollectionUtils.isEmpty(indicators)) {
                log.debug("LOG10046:用户无指标得分记录, authprjId={}, userId={}", authprjId, userId);
                return BigDecimal.ZERO;
            }

            BigDecimal totalScore = indicators.stream()
                .map(AuthprjResultUserIndicatorPO::getScore)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.debug("LOG67680:计算用户总得分完成, authprjId={}, userId={}, totalScore={}, indicatorCount={}",
                authprjId, userId, totalScore, indicators.size());

            return totalScore;

        } catch (Exception e) {
            log.error("LOG67690:计算用户总得分失败, authprjId={}, userId={}, error={}",
                authprjId, userId, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

}
